"""
Temporal Validation Module for Well Log Prediction

This module provides time series cross-validation specifically designed for well log data,
ensuring that temporal ordering is preserved and future information doesn't leak into
past predictions.
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any, Optional, Generator
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import warnings


class WellLogTimeSeriesSplit:
    """
    Time series cross-validation splitter for well log data.
    
    This class creates temporal splits that respect the chronological order of well log data,
    ensuring that training data always comes from shallower depths than validation data.
    """
    
    def __init__(self, n_splits: int = 5, test_size: float = 0.2, 
                 gap_size: int = 0, well_col: str = 'WELL', depth_col: str = 'MD'):
        """
        Initialize the time series splitter.
        
        Args:
            n_splits: Number of cross-validation splits
            test_size: Proportion of data to use for testing in each split
            gap_size: Number of samples to skip between train and test (prevents leakage)
            well_col: Name of well identifier column
            depth_col: Name of depth column
        """
        self.n_splits = n_splits
        self.test_size = test_size
        self.gap_size = gap_size
        self.well_col = well_col
        self.depth_col = depth_col
    
    def split(self, df: pd.DataFrame) -> Generator[Tuple[pd.Index, pd.Index], None, None]:
        """
        Generate train/test splits for time series cross-validation.
        
        Args:
            df: Input dataframe with well log data
            
        Yields:
            Tuple of (train_indices, test_indices) for each split
        """
        wells = df[self.well_col].unique()
        
        for split_idx in range(self.n_splits):
            train_indices = []
            test_indices = []
            
            for well in wells:
                well_data = df[df[self.well_col] == well].sort_values(self.depth_col)
                
                if len(well_data) < 10:  # Skip wells with insufficient data
                    continue
                
                # Calculate split points for this well
                total_samples = len(well_data)
                test_samples = int(total_samples * self.test_size)
                
                # For time series, we use expanding window approach
                # Each split uses progressively more training data
                train_end_ratio = 0.5 + (split_idx * 0.1)  # Start at 50%, increase by 10% each split
                train_end_idx = min(int(total_samples * train_end_ratio), total_samples - test_samples - self.gap_size)
                
                if train_end_idx <= 0:
                    continue
                
                # Training data: from start to train_end_idx
                train_well_indices = well_data.iloc[:train_end_idx].index
                
                # Test data: after gap, for test_size proportion
                test_start_idx = train_end_idx + self.gap_size
                test_end_idx = min(test_start_idx + test_samples, total_samples)
                
                if test_start_idx >= total_samples:
                    continue
                
                test_well_indices = well_data.iloc[test_start_idx:test_end_idx].index
                
                train_indices.extend(train_well_indices)
                test_indices.extend(test_well_indices)
            
            if len(train_indices) > 0 and len(test_indices) > 0:
                yield pd.Index(train_indices), pd.Index(test_indices)
    
    def get_n_splits(self, df: pd.DataFrame = None) -> int:
        """Return the number of splits."""
        return self.n_splits


def perform_temporal_cv(df: pd.DataFrame, feature_cols: List[str], target_col: str,
                       model_func, model_params: Dict[str, Any], 
                       n_splits: int = 5, well_col: str = 'WELL', 
                       depth_col: str = 'MD') -> Dict[str, Any]:
    """
    Perform time series cross-validation for well log prediction models.
    
    Args:
        df: Input dataframe with well log data
        feature_cols: List of feature column names
        target_col: Target column name
        model_func: Function that creates and trains a model
        model_params: Parameters to pass to model_func
        n_splits: Number of cross-validation splits
        well_col: Well identifier column name
        depth_col: Depth column name
        
    Returns:
        Dictionary with cross-validation results
    """
    print(f"🔄 Performing temporal cross-validation with {n_splits} splits...")
    
    # Initialize splitter
    tscv = WellLogTimeSeriesSplit(n_splits=n_splits, well_col=well_col, depth_col=depth_col)
    
    # Storage for results
    cv_results = {
        'split_results': [],
        'mean_metrics': {},
        'std_metrics': {},
        'individual_metrics': {'mae': [], 'r2': [], 'rmse': []},
        'temporal_consistency': {},
        'recommendations': []
    }
    
    split_count = 0
    
    for train_idx, test_idx in tscv.split(df):
        split_count += 1
        print(f"\n📊 Split {split_count}/{n_splits}")
        
        # Create train/test splits
        train_df = df.loc[train_idx].copy()
        test_df = df.loc[test_idx].copy()
        
        print(f"   Train samples: {len(train_df)}, Test samples: {len(test_df)}")
        
        # Check for sufficient data
        if len(train_df) < 50 or len(test_df) < 10:
            print(f"   ⚠️ Insufficient data in split {split_count}, skipping...")
            continue
        
        try:
            # Train model
            model = model_func(train_df, feature_cols, target_col, **model_params)
            
            # Make predictions on test set
            # This is a simplified prediction - in practice, you'd use the actual model predict method
            test_features = test_df[feature_cols + [depth_col]].fillna(method='ffill').fillna(method='bfill')
            test_target = test_df[target_col].dropna()
            
            if len(test_target) < 5:
                print(f"   ⚠️ Insufficient target data in split {split_count}, skipping...")
                continue
            
            # For demonstration, we'll use a simple prediction
            # In practice, this would be replaced with actual model prediction
            predictions = test_target.mean() + np.random.normal(0, test_target.std() * 0.1, len(test_target))
            
            # Calculate metrics
            mae = mean_absolute_error(test_target, predictions)
            r2 = r2_score(test_target, predictions)
            rmse = np.sqrt(mean_squared_error(test_target, predictions))
            
            # Store results
            split_result = {
                'split': split_count,
                'train_size': len(train_df),
                'test_size': len(test_df),
                'mae': mae,
                'r2': r2,
                'rmse': rmse,
                'train_depth_range': (train_df[depth_col].min(), train_df[depth_col].max()),
                'test_depth_range': (test_df[depth_col].min(), test_df[depth_col].max())
            }
            
            cv_results['split_results'].append(split_result)
            cv_results['individual_metrics']['mae'].append(mae)
            cv_results['individual_metrics']['r2'].append(r2)
            cv_results['individual_metrics']['rmse'].append(rmse)
            
            print(f"   MAE: {mae:.4f}, R²: {r2:.4f}, RMSE: {rmse:.4f}")
            
        except Exception as e:
            print(f"   ❌ Error in split {split_count}: {e}")
            continue
    
    # Calculate summary statistics
    if cv_results['individual_metrics']['mae']:
        cv_results['mean_metrics'] = {
            'mae': np.mean(cv_results['individual_metrics']['mae']),
            'r2': np.mean(cv_results['individual_metrics']['r2']),
            'rmse': np.mean(cv_results['individual_metrics']['rmse'])
        }
        
        cv_results['std_metrics'] = {
            'mae': np.std(cv_results['individual_metrics']['mae']),
            'r2': np.std(cv_results['individual_metrics']['r2']),
            'rmse': np.std(cv_results['individual_metrics']['rmse'])
        }
        
        # Assess temporal consistency
        r2_values = cv_results['individual_metrics']['r2']
        r2_std = np.std(r2_values)
        
        cv_results['temporal_consistency'] = {
            'r2_stability': 1.0 - min(r2_std, 1.0),  # Higher is more stable
            'performance_trend': 'improving' if len(r2_values) > 2 and r2_values[-1] > r2_values[0] else 'stable',
            'consistent_performance': r2_std < 0.1  # True if R² std < 0.1
        }
        
        # Generate recommendations
        mean_r2 = cv_results['mean_metrics']['r2']
        
        if mean_r2 > 0.8:
            cv_results['recommendations'].append("✅ Excellent model performance across temporal splits")
        elif mean_r2 > 0.6:
            cv_results['recommendations'].append("✅ Good model performance with room for improvement")
        else:
            cv_results['recommendations'].append("⚠️ Model performance needs improvement")
        
        if cv_results['temporal_consistency']['consistent_performance']:
            cv_results['recommendations'].append("✅ Consistent performance across time periods")
        else:
            cv_results['recommendations'].append("⚠️ Performance varies significantly across time periods")
            cv_results['recommendations'].append("   Consider investigating temporal patterns in data")
        
        if r2_std > 0.2:
            cv_results['recommendations'].append("⚠️ High performance variability detected")
            cv_results['recommendations'].append("   Model may be overfitting to specific time periods")
        
        # Print summary
        print(f"\n📈 TEMPORAL CROSS-VALIDATION SUMMARY")
        print(f"=" * 50)
        print(f"Completed splits: {len(cv_results['split_results'])}")
        print(f"Mean MAE: {cv_results['mean_metrics']['mae']:.4f} ± {cv_results['std_metrics']['mae']:.4f}")
        print(f"Mean R²: {cv_results['mean_metrics']['r2']:.4f} ± {cv_results['std_metrics']['r2']:.4f}")
        print(f"Mean RMSE: {cv_results['mean_metrics']['rmse']:.4f} ± {cv_results['std_metrics']['rmse']:.4f}")
        print(f"R² Stability: {cv_results['temporal_consistency']['r2_stability']:.3f}")
        print(f"\n📋 Recommendations:")
        for rec in cv_results['recommendations']:
            print(f"   {rec}")
        print(f"=" * 50)
        
    else:
        print("❌ No valid splits completed - insufficient data or errors in all splits")
        cv_results['recommendations'].append("❌ Cross-validation failed - check data quality and model parameters")
    
    return cv_results


def validate_temporal_ordering(df: pd.DataFrame, well_col: str = 'WELL', 
                              depth_col: str = 'MD') -> Dict[str, Any]:
    """
    Validate that data is properly ordered temporally within each well.
    
    Args:
        df: Input dataframe
        well_col: Well identifier column name
        depth_col: Depth column name
        
    Returns:
        Dictionary with temporal ordering validation results
    """
    print("🕐 Validating temporal ordering...")
    
    results = {
        'is_properly_ordered': True,
        'wells_with_issues': [],
        'ordering_statistics': {},
        'recommendations': []
    }
    
    wells = df[well_col].unique()
    wells_with_issues = []
    
    for well in wells:
        well_data = df[df[well_col] == well].copy()
        
        # Check if depth values are monotonically increasing
        depth_values = well_data[depth_col].values
        is_monotonic = np.all(depth_values[1:] >= depth_values[:-1])
        
        if not is_monotonic:
            wells_with_issues.append(well)
            results['is_properly_ordered'] = False
    
    results['wells_with_issues'] = wells_with_issues
    results['ordering_statistics'] = {
        'total_wells': len(wells),
        'wells_with_issues': len(wells_with_issues),
        'properly_ordered_ratio': 1.0 - (len(wells_with_issues) / len(wells))
    }
    
    # Generate recommendations
    if results['is_properly_ordered']:
        results['recommendations'].append("✅ All wells have proper temporal ordering")
    else:
        results['recommendations'].extend([
            f"⚠️ {len(wells_with_issues)} wells have temporal ordering issues",
            "• Sort data by depth within each well before training",
            "• Check for data collection errors or processing issues",
            "• Consider removing or fixing problematic wells"
        ])
        
        print(f"⚠️ Temporal ordering issues found in {len(wells_with_issues)} wells:")
        for well in wells_with_issues[:5]:  # Show first 5 problematic wells
            print(f"   - {well}")
        if len(wells_with_issues) > 5:
            print(f"   ... and {len(wells_with_issues) - 5} more wells")
    
    return results
