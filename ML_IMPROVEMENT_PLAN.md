# Project: SAITS & BRITS Training Process Enhancement

**Objective:** To refactor the existing machine learning pipeline to eliminate data leakage, introduce realistic data handling, and implement robust validation and monitoring, based on the `SAITS_BRITS_TRAINING_FIX_GUIDE.md` and our collaborative planning.

---

### **Phase 1: Critical Fixes - Advanced Data Splitting and Correct Evaluation**

This phase addresses the most severe data leakage issues by implementing a robust, user-controlled splitting strategy.

*   **Step 1.1: Implement Flexible, User-Controlled Splitting**
    *   **File:** `ml_core.py`
    *   **Action:**
        1.  A new function, `create_flexible_split`, will be created. This function allows for the manual specification of training and test wells, providing full control over the final evaluation set.
        2.  A temporal split will be applied *only* to the designated training wells to create a validation set for hyperparameter tuning, preventing data leakage during the model development cycle.
        3.  **Proposed Function:**
            ```python
            def create_flexible_split(df, well_col='WELL', depth_col='MD', 
                                      train_wells=None, test_wells=None, 
                                      val_depth_ratio=0.3, random_state=42):
                """
                Creates a highly flexible train/validation/test split.

                It allows for manual specification of training and test wells. A temporal split
                is then applied to the training wells to create a validation set.

                Args:
                    df: Input dataframe.
                    well_col: Name of the well identifier column.
                    depth_col: Name of the depth/time column.
                    train_wells: (Optional) A list of well names to use for training/validation.
                    test_wells: (Optional) A list of well names to hold out for the test set.
                    val_depth_ratio: Proportion of depth to use for validation within the training wells.
                    random_state: Random seed for reproducibility.

                Returns:
                    train_df, val_df, test_df
                """
                if train_wells is None or test_wells is None:
                    raise ValueError("You must provide a list of 'train_wells' and 'test_wells'.")

                # Ensure no overlap between train and test wells
                if set(train_wells) & set(test_wells):
                    raise ValueError("Train and test wells must not overlap.")

                # The final test set is composed of the user-specified held-out wells
                test_df = df[df[well_col].isin(test_wells)].copy()
                
                train_dfs, val_dfs = [], []
                
                # Apply temporal split on the user-specified training wells
                for well in train_wells:
                    well_df = df[df[well_col] == well].sort_values(depth_col)
                    if len(well_df) < 20:
                        print(f"Warning: Well '{well}' has insufficient data and will be skipped.")
                        continue
                    
                    # Split the training well into a shallow part (for training) and a deeper part (for validation)
                    val_start_index = int(len(well_df) * (1 - val_depth_ratio))
                    
                    train_dfs.append(well_df.iloc[:val_start_index])
                    val_dfs.append(well_df.iloc[val_start_index:])
                    
                train_df = pd.concat(train_dfs, ignore_index=True) if train_dfs else pd.DataFrame()
                val_df = pd.concat(val_dfs, ignore_index=True) if val_dfs else pd.DataFrame()
                
                print("Flexible Split Report:")
                print(f"  - Wells for Training/Validation: {len(train_wells)}")
                print(f"  - Wells for Final Testing: {len(test_wells)}")
                print(f"  - Train Samples (Shallow part of train wells): {len(train_df)}")
                print(f"  - Validation Samples (Deeper part of train wells): {len(val_df)}")
                print(f"  - Test Samples (Entirely separate wells): {len(test_df)}")
                
                return train_df, val_df, test_df
            ```
        4.  **Diagram: User-Controlled Hybrid Split**
            ```mermaid
            graph TD
                A[All Wells] --> B{User Manually Selects Wells};
                B -- "e.g., [Well A, Well B]" --> C[Training/Validation Wells];
                B -- "e.g., [Well X, Well Y]" --> D[Held-out Test Wells];

                subgraph "Temporal Split on Training Wells"
                    C --> E{For each well...};
                    E --> F[Sort by Depth];
                    F --> G{Split by Depth (e.g., 70/30)};
                    G -- 70% Shallow --> H(Train Set);
                    G -- 30% Deeper --> I(Validation Set);
                end

                subgraph "Final, Unbiased Evaluation"
                   D --> J(Final Test Set);
                end
            ```

*   **Step 1.2: Fix Evaluation Logic**
    *   **File:** `ml_core.py`
    *   **Action:** Replace the current evaluation logic in `impute_logs_deep()` with the new, correctly masked evaluation logic that distinguishes between artificial and natural missing values, as specified in the guide.

---

### **Phase 2: Improving Data Realism and Proactive Leakage Detection**

This phase focuses on making the training data and process more representative of real-world scenarios.

*   **Step 2.1: Implement Realistic Missing Data Patterns**
    *   **File:** `data_handler.py`
    *   **Action:**
        1.  Add the new `introduce_realistic_missingness` function.
        2.  Update the `impute_logs_deep()` function in `ml_core.py` to call this new function.
        3.  Add the more advanced `generate_geological_missing_patterns` function for future enhancements.

*   **Step 2.2: Create Data Leakage Detection Module**
    *   **File (New):** `data_leakage_detector.py`
    *   **Action:**
        1.  Create the new file and implement `detect_perfect_correlation_leakage` and `validate_temporal_split`.
        2.  Integrate calls to these functions in `ml_core.py` after data splitting and evaluation to automatically flag potential issues.

---

### **Phase 3: Advanced Validation and Monitoring**

This phase implements best practices for robust model validation and long-term performance tracking.

*   **Step 3.1: Implement Time Series Cross-Validation**
    *   **File (New):** `temporal_validation.py`
    *   **Action:** Create the new file and implement the `WellLogTimeSeriesSplit` class and `perform_temporal_cv` function.

*   **Step 3.2: Implement Performance Monitoring**
    *   **File (Update):** `utils/performance_monitor.py`
    *   **Action:** Enhance the existing `performance_monitor.py` to align with the guide's more comprehensive `ModelPerformanceMonitor` class, including alerting for anomalies like suspiciously high R² values.

---

### **Phase 4: Final Touches and Testing**

*   **Step 4.1: Update Model Configuration**
    *   **File:** `models/advanced_models/saits_model.py`
    *   **Action:** Add the `validate_causality` parameter to the `__init__` method of the `SAITSModel` class.

*   **Step 4.2: Create Dedicated Test Suite**
    *   **File (New):** `test_temporal_fixes.py`
    *   **Action:** Create this new test file and implement the test functions `test_temporal_splitting`, `test_realistic_missingness`, and `test_evaluation_logic`.